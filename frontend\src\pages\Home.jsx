import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SEO, { pageSEO } from '../components/SEO';
import Logo from '../components/Logo';
import { ArrowRight, Camera, Users, Zap, Shield, Star, CheckCircle } from 'lucide-react';

// Hero Section Component
const HeroSection = () => {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate('/generate');
  };

  return (
    <div className="relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="text-center">
          {/* Main heading */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
            Create images using{' '}
            <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
              AI
            </span>
          </h1>

          {/* Subheading */}
          <h2 className="text-xl md:text-2xl lg:text-3xl font-semibold text-gray-300 mb-8">
            Generate professional portraits and themed headshots
          </h2>

          {/* Description */}
          <p className="text-lg md:text-xl text-gray-400 max-w-4xl mx-auto mb-12 leading-relaxed">
            Gen Free AI is an AI-powered tool designed to craft perfect professional and casual headshots.
            Ideal for resume photos, Instagram profile pictures, social media images, and beyond.
          </p>

          {/* CTA Button */}
          <div className="mb-16">
            <button
              onClick={handleGetStarted}
              className="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-lg font-semibold rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              <span>Start Creating Now For Free</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Features Section Component
const FeaturesSection = () => {
  const features = [
    {
      icon: <Camera className="w-8 h-8" />,
      title: "Professional Quality",
      description: "Generate high-resolution, professional-grade images perfect for any use case"
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Lightning Fast",
      description: "Create stunning images in seconds with our advanced AI technology"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Multiple Styles",
      description: "Choose from various themes including business, casual, creative, and more"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "100% Free",
      description: "No hidden costs, no subscriptions. Generate unlimited images completely free"
    }
  ];

  return (
    <div className="py-20 bg-slate-800/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Why Choose Gen Free AI?
          </h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Experience the power of AI image generation with professional results
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-slate-800/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-700/50 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105"
            >
              <div className="text-blue-400 mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-400 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Use Cases Section Component
const UseCasesSection = () => {
  const useCases = [
    "Professional headshots for LinkedIn",
    "Social media profile pictures",
    "Dating app photos",
    "Resume and CV photos",
    "Business portraits",
    "Creative artistic images",
    "Team member photos",
    "Personal branding images"
  ];

  return (
    <div className="py-20 bg-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Perfect For Every Occasion
          </h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            AI generated business headshots, professional images, creative images.
            Explore AI image generation themes like nightlife, cyberpunk, glamour, business professional, and more.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {useCases.map((useCase, index) => (
            <div
              key={index}
              className="flex items-center gap-3 bg-slate-800/60 backdrop-blur-sm rounded-lg p-4 border border-slate-700/50 hover:border-blue-500/50 transition-all duration-300"
            >
              <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
              <span className="text-gray-300">{useCase}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Stats Section Component
const StatsSection = () => {
  const stats = [
    { number: "1M+", label: "Images Generated" },
    { number: "50K+", label: "Happy Users" },
    { number: "100%", label: "Free Forever" },
    { number: "24/7", label: "Available" }
  ];

  return (
    <div className="py-16 bg-gradient-to-r from-blue-600/10 to-purple-600/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                {stat.number}
              </div>
              <div className="text-gray-400 text-sm md:text-base">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const Home = () => {
  useEffect(() => {
    // Scroll to top when page loads
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <div className="relative min-h-screen bg-slate-900">
      {/* SEO */}
      <SEO
        title={pageSEO.home.title}
        description={pageSEO.home.description}
        keywords={pageSEO.home.keywords}
      />

      {/* Hero Section */}
      <HeroSection />

      {/* Features Section */}
      <FeaturesSection />

      {/* Use Cases Section */}
      <UseCasesSection />

      {/* Stats Section */}
      <StatsSection />
    </div>
  );
};

export default Home;
