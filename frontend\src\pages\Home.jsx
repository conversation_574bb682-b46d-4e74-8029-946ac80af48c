import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SEO, { pageSEO } from '../components/SEO';
import Logo from '../components/Logo';
import { Sparkles, Zap, Heart, Star, ArrowRight, Palette, Wand2, Image as ImageIcon } from 'lucide-react';

// Welcome Screen Component
const WelcomeScreen = () => {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate('/generate');
  };
  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="max-w-4xl mx-auto text-center">
        {/* Animated Logo */}
        <div className="flex justify-center mb-8 animate-bounce-in" style={{ animationDelay: '0.2s' }}>
          <div className="relative">
            <Logo size="2xl" />
            <div className="absolute -top-2 -right-2 animate-pulse">
              <Sparkles className="w-8 h-8 text-yellow-500" />
            </div>
          </div>
        </div>

        {/* Main Title */}
        <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent animate-gradient-x">
            Welcome to
          </span>
          <br />
          <span className="bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 bg-clip-text text-transparent animate-gradient-x">
            Gen Free AI
          </span>
        </h1>

        {/* Subtitle */}
        <p className="text-xl md:text-2xl text-gray-400 dark:text-gray-300 mb-8 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
          Transform your imagination into stunning AI-generated artwork
        </p>

        {/* Feature Icons */}
        <div className="flex justify-center gap-6 mb-12 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
          <div className="flex flex-col items-center group">
            <div className="p-4 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900 rounded-2xl mb-2 group-hover:scale-110 transition-transform duration-300">
              <Palette className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <span className="text-sm font-medium text-gray-400 dark:text-gray-400">Create Art</span>
          </div>
          <div className="flex flex-col items-center group">
            <div className="p-4 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900 dark:to-cyan-900 rounded-2xl mb-2 group-hover:scale-110 transition-transform duration-300">
              <Zap className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <span className="text-sm font-medium text-gray-400 dark:text-gray-400">Lightning Fast</span>
          </div>
          <div className="flex flex-col items-center group">
            <div className="p-4 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 rounded-2xl mb-2 group-hover:scale-110 transition-transform duration-300">
              <Heart className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <span className="text-sm font-medium text-gray-400 dark:text-gray-400">100% Free</span>
          </div>
          <div className="flex flex-col items-center group">
            <div className="p-4 bg-gradient-to-br from-orange-100 to-red-100 dark:from-orange-900 dark:to-red-900 rounded-2xl mb-2 group-hover:scale-110 transition-transform duration-300">
              <ImageIcon className="w-8 h-8 text-orange-600 dark:text-orange-400" />
            </div>
            <span className="text-sm font-medium text-gray-400 dark:text-gray-400">High Quality</span>
          </div>
        </div>

        {/* Description */}
        <div className="max-w-2xl mx-auto mb-12 animate-fade-in-up" style={{ animationDelay: '1s' }}>
          <p className="text-lg text-gray-400 dark:text-gray-300 leading-relaxed mb-6">
            Unleash your creativity with our powerful AI image generator. Simply describe what you want to see,
            and watch as our advanced AI brings your vision to life in stunning detail.
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-400 dark:text-gray-400">
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-yellow-500" />
              <span>No signup required</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-yellow-500" />
              <span>Unlimited generations</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-yellow-500" />
              <span>High resolution</span>
            </div>
          </div>
        </div>

        {/* Get Started Button */}
        <div className="animate-bounce-in" style={{ animationDelay: '1.2s' }}>
          <button
            onClick={handleGetStarted}
            className="group relative inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 hover:from-purple-700 hover:via-pink-700 hover:to-orange-600 text-white text-xl font-bold rounded-2xl shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
          >
            <Wand2 className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
            <span>Get Started</span>
            <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />

            {/* Animated background */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
          </button>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 animate-float" style={{ animationDelay: '2s' }}>
          <Sparkles className="w-6 h-6 text-purple-400 opacity-60" />
        </div>
        <div className="absolute top-40 right-20 animate-float" style={{ animationDelay: '2.5s' }}>
          <Star className="w-5 h-5 text-pink-400 opacity-60" />
        </div>
        <div className="absolute bottom-40 left-20 animate-float" style={{ animationDelay: '3s' }}>
          <Heart className="w-4 h-4 text-red-400 opacity-60" />
        </div>
        <div className="absolute bottom-20 right-10 animate-float" style={{ animationDelay: '3.5s' }}>
          <Zap className="w-5 h-5 text-yellow-400 opacity-60" />
        </div>
      </div>
    </div>
  );
};

const Home = () => {
  useEffect(() => {
    // Scroll to top when page loads
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <div className="relative">
      {/* SEO */}
      <SEO
        title={pageSEO.home.title}
        description={pageSEO.home.description}
        keywords={pageSEO.home.keywords}
      />

      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 -z-10"></div>

      {/* Content container */}
      <div className="relative z-10 py-8 px-4 sm:px-6 lg:px-8">
        <WelcomeScreen />
      </div>
    </div>
  );
};

export default Home;
