import { BrowserRouter as Router, Routes, Route, useNavigate } from 'react-router-dom';
import { NotificationProvider, useNotification } from './contexts/NotificationContext';
import StyledSocialIcons from './components/StyledSocialIcons';
import Navigation from './components/Navigation';
import ScrollToTop from './components/ScrollToTop';
import Home from './pages/Home';
import Generate from './pages/Generate';
import History from './pages/History';
import About from './pages/About';
import FAQ from './pages/FAQ';
import Terms from './pages/Terms';
import Blog from './pages/Blog';
import AdminPanel from './pages/AdminPanel';
import ImageDownload from './pages/ImageDownload';
import NotFound from './pages/NotFound';
import HistoryNotification from './components/HistoryNotification';
import ReactGA from "react-ga4";
import { useEffect } from 'react';

const AppContent = () => {
  ReactGA.initialize("G-6RJ0LDGRT3");
useEffect(() => {
  ReactGA.send({ hitType: "pageview", page: window.location.pathname, title:"Gen Free AI" });
}, []);




  const navigate = useNavigate();
  const { notification, hideNotification } = useNotification();

  const handleHistoryClick = () => {
    hideNotification();
  };

  const handleTermsClick = () => {
    navigate('/terms');
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Scroll to top on route change */}
      <ScrollToTop />

      {/* Navigation */}
      <Navigation />

      {/* Main content */}
      <main className="relative">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/generate" element={<Generate />} />
          <Route path="/history" element={<History />} />
          <Route path="/about" element={<About />} />
          <Route path="/faq" element={<FAQ />} />
          <Route path="/terms" element={<Terms />} />
          <Route path="/blog" element={<Blog />} />
          <Route path="/blog/:slug" element={<Blog />} />
          <Route path="/image/download" element={<ImageDownload />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 border-t border-gray-700 mt-16 animate-fade-in-up">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center space-y-6">
            <p className="text-gray-300">
              Powered by <span className="bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 bg-clip-text text-transparent animate-gradient-x font-semibold">Gen Free AI</span> • Create stunning images from text descriptions
            </p>

            {/* Social Media Links */}
            <StyledSocialIcons compact={true} />

            <div className="flex justify-center space-x-6 text-sm text-gray-400 mt-2">
              <span>© 2025 Gen Free AI. All rights reserved.</span>
              <span>•</span>
              <span>Powered by genfreeai</span>
              <span>•</span>
              <button
                onClick={handleTermsClick}
                className="hover:text-gray-300 transition-colors cursor-pointer"
              >
                Terms & Conditions
              </button>
            </div>
          </div>
        </div>
      </footer>

      {/* History Notification */}
      <HistoryNotification
        isVisible={notification.isVisible && notification.type === 'history'}
        onClose={hideNotification}
        onHistoryClick={handleHistoryClick}
        imageCount={notification.imageCount}
      />
    </div>
  );
};

function App() {
  return (
    <NotificationProvider>
      <Router>
        <AppContent />
      </Router>
    </NotificationProvider>
  );
}

export default App;